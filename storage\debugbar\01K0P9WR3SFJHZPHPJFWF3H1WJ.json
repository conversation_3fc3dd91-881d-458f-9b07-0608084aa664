{"__meta": {"id": "01K0P9WR3SFJHZPHPJFWF3H1WJ", "datetime": "2025-07-21 10:53:53", "utime": **********.65889, "method": "GET", "uri": "/forms/test-form-fdk8c4/builder", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 17, "start": **********.241859, "end": **********.658911, "duration": 0.4170520305633545, "duration_str": "417ms", "measures": [{"label": "Booting", "start": **********.241859, "relative_start": 0, "end": **********.485128, "relative_end": **********.485128, "duration": 0.****************, "duration_str": "243ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.485145, "relative_start": 0.*************, "end": **********.658913, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.528957, "relative_start": 0.****************, "end": **********.532894, "relative_end": **********.532894, "duration": 0.0039370059967041016, "duration_str": "3.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.615594, "relative_start": 0.****************, "end": **********.655835, "relative_end": **********.655835, "duration": 0.*****************, "duration_str": "40.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: forms.builder", "start": **********.619433, "relative_start": 0.*****************, "end": **********.619433, "relative_end": **********.619433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.app", "start": **********.626313, "relative_start": 0.*****************, "end": **********.626313, "relative_end": **********.626313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.navigation", "start": **********.629549, "relative_start": 0.38769006729125977, "end": **********.629549, "relative_end": **********.629549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.application-logo", "start": **********.631745, "relative_start": 0.38988614082336426, "end": **********.631745, "relative_end": **********.631745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.639092, "relative_start": 0.3972330093383789, "end": **********.639092, "relative_end": **********.639092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.642221, "relative_start": 0.4003620147705078, "end": **********.642221, "relative_end": **********.642221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.645298, "relative_start": 0.4034390449523926, "end": **********.645298, "relative_end": **********.645298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.648632, "relative_start": 0.40677309036254883, "end": **********.648632, "relative_end": **********.648632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.649847, "relative_start": 0.4079880714416504, "end": **********.649847, "relative_end": **********.649847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown", "start": **********.650693, "relative_start": 0.40883398056030273, "end": **********.650693, "relative_end": **********.650693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.653197, "relative_start": 0.41133809089660645, "end": **********.653197, "relative_end": **********.653197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.654496, "relative_start": 0.41263699531555176, "end": **********.654496, "relative_end": **********.654496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.655279, "relative_start": 0.4134199619293213, "end": **********.655279, "relative_end": **********.655279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 25360400, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.20.0", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 13, "nb_templates": 13, "templates": [{"name": "forms.builder", "param_count": null, "params": [], "start": **********.619382, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/forms/builder.blade.phpforms.builder", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fforms%2Fbuilder.blade.php&line=1", "ajax": false, "filename": "builder.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.626281, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.navigation", "param_count": null, "params": [], "start": **********.629518, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/layouts/navigation.blade.phplayouts.navigation", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Flayouts%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "components.application-logo", "param_count": null, "params": [], "start": **********.631715, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/application-logo.blade.phpcomponents.application-logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fapplication-logo.blade.php&line=1", "ajax": false, "filename": "application-logo.blade.php", "line": "?"}}, {"name": "components.nav-link", "param_count": null, "params": [], "start": **********.639069, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/nav-link.blade.phpcomponents.nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fnav-link.blade.php&line=1", "ajax": false, "filename": "nav-link.blade.php", "line": "?"}}, {"name": "components.nav-link", "param_count": null, "params": [], "start": **********.642202, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/nav-link.blade.phpcomponents.nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fnav-link.blade.php&line=1", "ajax": false, "filename": "nav-link.blade.php", "line": "?"}}, {"name": "components.nav-link", "param_count": null, "params": [], "start": **********.645267, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/nav-link.blade.phpcomponents.nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fnav-link.blade.php&line=1", "ajax": false, "filename": "nav-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.64861, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.649815, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown", "param_count": null, "params": [], "start": **********.650662, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/dropdown.blade.phpcomponents.dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}}, {"name": "components.responsive-nav-link", "param_count": null, "params": [], "start": **********.653149, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/responsive-nav-link.blade.phpcomponents.responsive-nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fresponsive-nav-link.blade.php&line=1", "ajax": false, "filename": "responsive-nav-link.blade.php", "line": "?"}}, {"name": "components.responsive-nav-link", "param_count": null, "params": [], "start": **********.65446, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/responsive-nav-link.blade.phpcomponents.responsive-nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fresponsive-nav-link.blade.php&line=1", "ajax": false, "filename": "responsive-nav-link.blade.php", "line": "?"}}, {"name": "components.responsive-nav-link", "param_count": null, "params": [], "start": **********.655253, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/responsive-nav-link.blade.phpcomponents.responsive-nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fresponsive-nav-link.blade.php&line=1", "ajax": false, "filename": "responsive-nav-link.blade.php", "line": "?"}}]}, "queries": {"count": 9, "nb_statements": 8, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.013150000000000002, "accumulated_duration_str": "13.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.552165, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "formdb", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'qluF06ppugkf4Kh36jBalMJGdxPGUE5Woot2PqhQ' limit 1", "type": "query", "params": [], "bindings": ["qluF06ppugkf4Kh36jBalMJGdxPGUE5Woot2PqhQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.5609658, "duration": 0.00447, "duration_str": "4.47ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "formdb", "explain": null, "start_percent": 0, "width_percent": 33.992}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.580231, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "formdb", "explain": null, "start_percent": 33.992, "width_percent": 7.148}, {"sql": "select * from `forms` where `slug` = 'test-form-fdk8c4' limit 1", "type": "query", "params": [], "bindings": ["test-form-fdk8c4"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 965}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.587553, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:60", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=60", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "60"}, "connection": "formdb", "explain": null, "start_percent": 41.141, "width_percent": 10.19}, {"sql": "select * from `form_fields` where `form_fields`.`form_id` in (1) order by `order` asc, `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/FormBuilderController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormBuilderController.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.599672, "duration": 0.00277, "duration_str": "2.77ms", "memory": 0, "memory_str": null, "filename": "FormBuilderController.php:17", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/FormBuilderController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormBuilderController.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormBuilderController.php&line=17", "ajax": false, "filename": "FormBuilderController.php", "line": "17"}, "connection": "formdb", "explain": null, "start_percent": 51.331, "width_percent": 21.065}, {"sql": "select * from `conditional_rules` where `conditional_rules`.`form_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/FormBuilderController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormBuilderController.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.6041589, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "FormBuilderController.php:17", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/FormBuilderController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormBuilderController.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormBuilderController.php&line=17", "ajax": false, "filename": "FormBuilderController.php", "line": "17"}, "connection": "formdb", "explain": null, "start_percent": 72.395, "width_percent": 6.008}, {"sql": "select * from `translations` where exists (select * from `languages` where `translations`.`language_id` = `languages`.`id` and `code` = 'en') and `key` = 'nav.dashboard' limit 1", "type": "query", "params": [], "bindings": ["en", "nav.dashboard"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, {"index": 17, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "D:\\Projects\\form-builder\\app\\Helpers\\TranslationHelper.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.636188, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Translation.php:30", "source": {"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FTranslation.php&line=30", "ajax": false, "filename": "Translation.php", "line": "30"}, "connection": "formdb", "explain": null, "start_percent": 78.403, "width_percent": 10.19}, {"sql": "select * from `translations` where exists (select * from `languages` where `translations`.`language_id` = `languages`.`id` and `code` = 'en') and `key` = 'nav.my_forms' limit 1", "type": "query", "params": [], "bindings": ["en", "nav.my_forms"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, {"index": 17, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "D:\\Projects\\form-builder\\app\\Helpers\\TranslationHelper.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.640315, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Translation.php:30", "source": {"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FTranslation.php&line=30", "ajax": false, "filename": "Translation.php", "line": "30"}, "connection": "formdb", "explain": null, "start_percent": 88.593, "width_percent": 6.692}, {"sql": "select * from `translations` where exists (select * from `languages` where `translations`.`language_id` = `languages`.`id` and `code` = 'en') and `key` = 'nav.admin' limit 1", "type": "query", "params": [], "bindings": ["en", "nav.admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, {"index": 17, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "D:\\Projects\\form-builder\\app\\Helpers\\TranslationHelper.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.6433482, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Translation.php:30", "source": {"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FTranslation.php&line=30", "ajax": false, "filename": "Translation.php", "line": "30"}, "connection": "formdb", "explain": null, "start_percent": 95.285, "width_percent": 4.715}]}, "models": {"data": {"App\\Models\\Translation": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FTranslation.php&line=1", "ajax": false, "filename": "Translation.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Form": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FForm.php&line=1", "ajax": false, "filename": "Form.php", "line": "?"}}}, "count": 5, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 5}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => update,\n  target => App\\Models\\Form(id=1),\n  result => true,\n  user => 2,\n  arguments => [0 => Object(App\\Models\\Form)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1710431501 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Form(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Form(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Form)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1710431501\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.596204, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/forms/test-form-fdk8c4/builder", "action_name": "forms.builder", "controller_action": "App\\Http\\Controllers\\FormBuilderController@show", "uri": "GET forms/{form}/builder", "controller": "App\\Http\\Controllers\\FormBuilderController@show<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormBuilderController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormBuilderController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/FormBuilderController.php:13-22</a>", "middleware": "web, auth", "duration": "420ms", "peak_memory": "26MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2120093968 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2120093968\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2037671654 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2037671654\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-94849720 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/forms/test-form-fdk8c4/builder</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ms;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1412 characters\">__stripe_mid=45fe3c7d-c6f2-457a-9c9f-a107d38d1d54978fc7; _ga=GA1.1.192285856.1746099575; _ga_69MPZE94D5=GS1.1.1746099574.1.1.1746099605.0.0.0; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndUUkRKeEd3elBPclVweVUrakhwcFE9PSIsInZhbHVlIjoiUHhxUmdma0t6RHltV2pCOWlEbmlOcUUrS2VBOCtRVjNrcklHYlpnc3lLL0dQeXlhV0VhNzh0Um5SakFGVitSdVh3bGw2WGg3MjM4NTU4aEhDQXlXRSt1MEtUN0xoQ2tHWFl2T2l3RDAwY0N6ZUlqQ3BSMDNrRnlCYnNSRnZEZG9wZlN0eVQ1SHE5dk5Nbk1CWlZ1bjByWXE1SFFjNVBiZHliU0U1MVgrbk1wcFdndzBrWis2VTcwRTdEMFJMcVdMNk9HSlVtTVJKZHNHTHhGZUEyQ29TbjQvMnJJSmFCT3AyUDJxRXFvQUp0Yz0iLCJtYWMiOiJlN2FmNmI4MWJjMDZlMjVmZWEyMjA3NDBkNGQxMTA2ODIzZjVkNjgzZTIwOTlmNTgyMWVkNTI1NjY3ZTAxZjc3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjFQWUJDczE2QVVmdUU5MVdsRkhNdXc9PSIsInZhbHVlIjoibkVnVnRzMnAzQUU1bHo5ancvVkd1bTlKTkJYelJtV3M5U2FuenlhREthR3RMeDdOemduQXJGVWM5L2ptNmJKY1Z3dFV4QmlCQTBvOXVXZ1pNVTB5VmdjMHd0Wm1rZEpNb0Y4SU5CRW91SDIwRkpySWRsb1VFSUYzOVNEQ1JjN1giLCJtYWMiOiJmOWYwNmVjOWI1MWNiNjYwYTk3NDZhMzUyMmIwZDM3MGMxYjg5MjVjYzIyMzJhZGI4NzlkNjFmNmY5M2U3NjNjIiwidGFnIjoiIn0%3D; anggur_form_builder_session=eyJpdiI6IkluZHZ4NHlRa1N4MWg5ckwvaDVaTlE9PSIsInZhbHVlIjoiSncwQW9ycjhTNE5zMjI0RjlIL1U2ZzlpNmVUamllZ0kvdGMxVUJ0K0RZZ1lCaTI0TEhsSEVYVjBFdFhRZ0IwOXJJRkYvNm9XemU4UlZiT2NtMFJZSTFlVXBhMmpjbnFGN2hpOXJDc3FmV0JVNGRkU0pmeGVXVHV4S1dLaVFxc3oiLCJtYWMiOiJlNGNlOWUzYmJkMmEwODA3MmE2ODFhYzc1NjU3YWMzOGUzMjQyMzg3MWYyNmI1OTJhNzZlNjhlMjViZGU3YjVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-94849720\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_69MPZE94D5</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1gHSTSeUQOySw1eUCVEP4Y8fEcxiY6YiFKNN6MML</span>\"\n  \"<span class=sf-dump-key>anggur_form_builder_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qluF06ppugkf4Kh36jBalMJGdxPGUE5Woot2PqhQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1953021367 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 10:53:53 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1953021367\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1825123700 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1gHSTSeUQOySw1eUCVEP4Y8fEcxiY6YiFKNN6MML</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/form/test-form-fdk8c4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1825123700\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/forms/test-form-fdk8c4/builder", "action_name": "forms.builder", "controller_action": "App\\Http\\Controllers\\FormBuilderController@show"}, "badge": null}}