@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom components */
@layer components {
    .btn-primary {
        @apply bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition duration-200;
    }

    .btn-secondary {
        @apply bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-4 rounded-lg transition duration-200;
    }

    .btn-success {
        @apply bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-lg transition duration-200;
    }

    .btn-danger {
        @apply bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg transition duration-200;
    }

    .form-input {
        @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200;
    }

    .form-label {
        @apply block text-sm font-medium text-gray-700 mb-2;
    }

    .card {
        @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
    }

    .alert-success {
        @apply bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg;
    }

    .alert-error {
        @apply bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg;
    }

    .alert-info {
        @apply bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded-lg;
    }
}

/* Custom utilities */
@layer utilities {
    .text-shadow {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .bg-gradient-secondary {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
}

/* Form Builder specific styles */
.form-field {
    @apply border border-gray-300 rounded-lg p-4 mb-4 bg-white hover:shadow-md transition-shadow duration-200;
}

.form-field.dragging {
    @apply opacity-50 transform rotate-2;
}

.drop-zone {
    @apply border-2 border-dashed border-gray-300 rounded-lg p-8 text-center text-gray-500 min-h-[200px] flex items-center justify-center;
}

.drop-zone.drag-over {
    @apply border-blue-500 bg-blue-50 text-blue-600;
}

/* Mobile responsive improvements */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none;
    }

    .mobile-full {
        width: 100% !important;
    }

    .mobile-text-sm {
        font-size: 0.875rem !important;
    }
}

/* Loading animations */
.loading-spinner {
    @apply animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600;
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
}
