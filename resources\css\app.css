@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom components */
@layer components {
    .btn-primary {
        @apply bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition duration-200;
    }

    .btn-secondary {
        @apply bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-4 rounded-lg transition duration-200;
    }

    .btn-success {
        @apply bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-lg transition duration-200;
    }

    .btn-danger {
        @apply bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg transition duration-200;
    }

    .form-input {
        @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200;
    }

    .form-label {
        @apply block text-sm font-medium text-gray-700 mb-2;
    }

    .card {
        @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
    }

    .alert-success {
        @apply bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg;
    }

    .alert-error {
        @apply bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg;
    }

    .alert-info {
        @apply bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded-lg;
    }
}

/* Custom utilities */
@layer utilities {
    .text-shadow {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .bg-gradient-secondary {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
}

/* Form Builder specific styles */
.form-field {
    @apply border border-gray-300 rounded-lg p-4 mb-4 bg-white hover:shadow-md transition-shadow duration-200;
}

.form-field.dragging {
    @apply opacity-50 transform rotate-2;
}

.drop-zone {
    @apply border-2 border-dashed border-gray-300 rounded-lg p-8 text-center text-gray-500 min-h-[200px] flex items-center justify-center;
}

.drop-zone.drag-over {
    @apply border-blue-500 bg-blue-50 text-blue-600;
}

/* Sortable.js styles */
.sortable-ghost {
    @apply opacity-50 bg-blue-100 border-blue-300 border-dashed;
}

.sortable-chosen {
    @apply shadow-lg transform scale-105 z-50;
}

.sortable-drag {
    @apply opacity-80 transform rotate-2;
}

.sortable-fallback {
    @apply opacity-80 bg-white border-2 border-blue-500 shadow-xl z-50;
}

/* Drag and drop states */
body.dragging-field .drop-zone {
    @apply border-blue-500 bg-blue-50 border-solid;
}

body.dragging-field #form-fields {
    @apply border-2 border-dashed border-blue-400 bg-blue-50 rounded-lg min-h-[200px];
}

/* Enhanced drop zone */
#form-fields:empty {
    @apply min-h-[200px] flex items-center justify-center;
}

#form-fields:empty::before {
    content: "Drop fields here to build your form";
    @apply text-gray-500 text-center;
}

.field-type {
    transition: all 0.2s ease;
}

.field-type:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.field-type.dragging {
    @apply opacity-70 transform scale-95;
}

/* Field type colors - enhanced */
.field-type[data-type="text"] { @apply bg-blue-100 border-blue-300 hover:bg-blue-200; }
.field-type[data-type="email"] { @apply bg-green-100 border-green-300 hover:bg-green-200; }
.field-type[data-type="textarea"] { @apply bg-yellow-100 border-yellow-300 hover:bg-yellow-200; }
.field-type[data-type="number"] { @apply bg-pink-100 border-pink-300 hover:bg-pink-200; }
.field-type[data-type="password"] { @apply bg-orange-100 border-orange-300 hover:bg-orange-200; }
.field-type[data-type="url"] { @apply bg-cyan-100 border-cyan-300 hover:bg-cyan-200; }
.field-type[data-type="tel"] { @apply bg-lime-100 border-lime-300 hover:bg-lime-200; }
.field-type[data-type="select"] { @apply bg-purple-100 border-purple-300 hover:bg-purple-200; }
.field-type[data-type="radio"] { @apply bg-red-100 border-red-300 hover:bg-red-200; }
.field-type[data-type="checkbox"] { @apply bg-indigo-100 border-indigo-300 hover:bg-indigo-200; }
.field-type[data-type="date"] { @apply bg-teal-100 border-teal-300 hover:bg-teal-200; }
.field-type[data-type="time"] { @apply bg-emerald-100 border-emerald-300 hover:bg-emerald-200; }
.field-type[data-type="datetime"] { @apply bg-violet-100 border-violet-300 hover:bg-violet-200; }
.field-type[data-type="file"] { @apply bg-rose-100 border-rose-300 hover:bg-rose-200; }
.field-type[data-type="image"] { @apply bg-sky-100 border-sky-300 hover:bg-sky-200; }
.field-type[data-type="header"] { @apply bg-amber-100 border-amber-300 hover:bg-amber-200; }
.field-type[data-type="paragraph"] { @apply bg-slate-100 border-slate-300 hover:bg-slate-200; }
.field-type[data-type="divider"] { @apply bg-neutral-100 border-neutral-300 hover:bg-neutral-200; }
.field-type[data-type="rating"] { @apply bg-fuchsia-100 border-fuchsia-300 hover:bg-fuchsia-200; }
.field-type[data-type="range"] { @apply bg-stone-100 border-stone-300 hover:bg-stone-200; }
.field-type[data-type="captcha"] { @apply bg-red-200 border-red-400 hover:bg-red-300; }
.field-type[data-type="signature"] { @apply bg-green-200 border-green-400 hover:bg-green-300; }
.field-type[data-type="color"] { @apply bg-blue-200 border-blue-400 hover:bg-blue-300; }

/* Mobile responsive improvements */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none;
    }

    .mobile-full {
        width: 100% !important;
    }

    .mobile-text-sm {
        font-size: 0.875rem !important;
    }
}

/* Loading animations */
.loading-spinner {
    @apply animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600;
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
}
