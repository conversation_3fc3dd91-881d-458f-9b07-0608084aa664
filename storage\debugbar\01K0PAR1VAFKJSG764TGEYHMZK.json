{"__meta": {"id": "01K0PAR1VAFKJSG764TGEYHMZK", "datetime": "2025-07-21 11:08:48", "utime": **********.36299, "method": "GET", "uri": "/form/test-form-fdk8c4", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 5, "start": **********.012714, "end": **********.363006, "duration": 0.3502922058105469, "duration_str": "350ms", "measures": [{"label": "Booting", "start": **********.012714, "relative_start": 0, "end": **********.269148, "relative_end": **********.269148, "duration": 0.*****************, "duration_str": "256ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.269162, "relative_start": 0.*****************, "end": **********.363009, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "93.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.288636, "relative_start": 0.****************, "end": **********.291268, "relative_end": **********.291268, "duration": 0.*****************, "duration_str": "2.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.353151, "relative_start": 0.****************, "end": **********.361084, "relative_end": **********.361084, "duration": 0.007932901382446289, "duration_str": "7.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: forms.public", "start": **********.356142, "relative_start": 0.****************, "end": **********.356142, "relative_end": **********.356142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.20.0", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "forms.public", "param_count": null, "params": [], "start": **********.356112, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/forms/public.blade.phpforms.public", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fforms%2Fpublic.blade.php&line=1", "ajax": false, "filename": "public.blade.php", "line": "?"}}]}, "queries": {"count": 5, "nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0294, "accumulated_duration_str": "29.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.302344, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "formdb", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'z7cuUWZvMHcel8crP64YTDt0e7CYtduXqK1Ps7cI' limit 1", "type": "query", "params": [], "bindings": ["z7cuUWZvMHcel8crP64YTDt0e7CYtduXqK1Ps7cI"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.3072758, "duration": 0.02659, "duration_str": "26.59ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "formdb", "explain": null, "start_percent": 0, "width_percent": 90.442}, {"sql": "select * from `forms` where `slug` = 'test-form-fdk8c4' limit 1", "type": "query", "params": [], "bindings": ["test-form-fdk8c4"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 965}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.337177, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:60", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=60", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "60"}, "connection": "formdb", "explain": null, "start_percent": 90.442, "width_percent": 3.537}, {"sql": "select * from `form_fields` where `form_fields`.`form_id` in (1) order by `order` asc, `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/FormSubmissionController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormSubmissionController.php", "line": 21}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.3425, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "FormSubmissionController.php:21", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/FormSubmissionController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormSubmissionController.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormSubmissionController.php&line=21", "ajax": false, "filename": "FormSubmissionController.php", "line": "21"}, "connection": "formdb", "explain": null, "start_percent": 93.98, "width_percent": 3.095}, {"sql": "select * from `conditional_rules` where `conditional_rules`.`form_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/FormSubmissionController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormSubmissionController.php", "line": 21}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.34456, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "FormSubmissionController.php:21", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/FormSubmissionController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormSubmissionController.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormSubmissionController.php&line=21", "ajax": false, "filename": "FormSubmissionController.php", "line": "21"}, "connection": "formdb", "explain": null, "start_percent": 97.075, "width_percent": 2.925}]}, "models": {"data": {"App\\Models\\Form": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FForm.php&line=1", "ajax": false, "filename": "Form.php", "line": "?"}}}, "count": 1, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/form/test-form-fdk8c4", "action_name": "form.show", "controller_action": "App\\Http\\Controllers\\FormSubmissionController@show", "uri": "GET form/{form}", "controller": "App\\Http\\Controllers\\FormSubmissionController@show<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormSubmissionController.php&line=15\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormSubmissionController.php&line=15\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/FormSubmissionController.php:15-26</a>", "middleware": "web", "duration": "352ms", "peak_memory": "24MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1430827496 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1430827496\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1500036755 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1500036755\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-690495619 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/form/test-form-fdk8c4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ms;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1954 characters\">__stripe_mid=45fe3c7d-c6f2-457a-9c9f-a107d38d1d54978fc7; _ga=GA1.1.192285856.1746099575; _ga_69MPZE94D5=GS1.1.1746099574.1.1.1746099605.0.0.0; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndUUkRKeEd3elBPclVweVUrakhwcFE9PSIsInZhbHVlIjoiUHhxUmdma0t6RHltV2pCOWlEbmlOcUUrS2VBOCtRVjNrcklHYlpnc3lLL0dQeXlhV0VhNzh0Um5SakFGVitSdVh3bGw2WGg3MjM4NTU4aEhDQXlXRSt1MEtUN0xoQ2tHWFl2T2l3RDAwY0N6ZUlqQ3BSMDNrRnlCYnNSRnZEZG9wZlN0eVQ1SHE5dk5Nbk1CWlZ1bjByWXE1SFFjNVBiZHliU0U1MVgrbk1wcFdndzBrWis2VTcwRTdEMFJMcVdMNk9HSlVtTVJKZHNHTHhGZUEyQ29TbjQvMnJJSmFCT3AyUDJxRXFvQUp0Yz0iLCJtYWMiOiJlN2FmNmI4MWJjMDZlMjVmZWEyMjA3NDBkNGQxMTA2ODIzZjVkNjgzZTIwOTlmNTgyMWVkNTI1NjY3ZTAxZjc3IiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjJhTzJPcS8rT3Vpa2lsZHBoVW9pMFE9PSIsInZhbHVlIjoiRitKYXJUQjRaRFlrTmxHdHJHRmlDQTYyYkVnbHNLdjQyeExjbTdKQW1MQ0RGWGk5ayswVVZDeTJBaDFhc29nWU5NL3FuYWpNcTYzdmJMbHJyd0NmZEJsTUx0MU1JN3pOMDZkK1lLT3FodFR5dUpFb3QvMmtSQ2tHNlZMUzhpR0N5ZGNXT2NKNXhGZ2FRb3h4RVNDd01nS2JsaHZWVDVmbzA4RXpJdDJzRkJrdUZrVkZzN0hwcE1HVzBZVXlwOHM1REROZFpEQXZ4elZRSndQYnVTekVOT2RiZEJob24xa0p5SW5MQlpkVytPRT0iLCJtYWMiOiI4NGE2OWUwYTQzMTQ3YjVmOTE5MTg0MjQ4MmMwNjFiYmMwYjIzZDUzYmVlZGNiNzc0MTI0OTE3MjJmMTMxYzU1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ijl0eCtsb1puK0dYTzIyQmlwdWlzUnc9PSIsInZhbHVlIjoia1Azd3Nma01GdWlSUUN4OGZ3SWk2VXVlZVNCVVEwTXR6c3BBU3JjSmc0cXd6a254SC8vTTFhRm9jQllaczIxb0ZadlJtdkd2eGJjT2ZNTWZrL1MwMnFkTXJhMXlMbDd5QytKQmREZEgzWElpTmVZRC9HSlRhaUR2WnNWL2xqc3IiLCJtYWMiOiI0ZWEwMDFjYzEyMmEyNzIwMDJiYmI1ZDRkZjA0NmExZDc5NzhiMDY5NDZiMTgwYjU4ZTc4N2JmZWY4OTg1YTFkIiwidGFnIjoiIn0%3D; anggur_form_builder_session=eyJpdiI6Ik1WZ0I4aFVwSGVNM0NONFFoZDFpdXc9PSIsInZhbHVlIjoibnUvc0w0WVJwNHZreGM1eGNzdkVOaXVQajJCSXNkeDM5bUJDSnFNbThmSFIrQnpNSE41RVJDdUFxb3czQjdBZWhzYlREeEdYSmFBeTM3WmVrN0d3NEhjMFJWVEdCNFIrb1Zzd0hLenVqZndraHFzcTZhSXV2RHVHQTBjVkFsQS8iLCJtYWMiOiJjMGYzZGMzZmZjY2RiMWVjOWEwYTIzMWIyODg5Y2YwZGNlNTA1Y2E5YjQ5ZmRiNzdhY2M1MDNmODc0MWExN2NlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-690495619\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-353980444 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_69MPZE94D5</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">3|gMZxXv5Ii5rSqXOhPwt4WUAe9J0MF6oAvy6uxcGtrEPX85bYnLwVNmMAHuP8|$2y$12$MC09iR4PkyqIkar/6cEeMeJ8ZZEJiM12UAj.VsXgiVELOJcpz7NIG</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LrSn8kuzi0SFtXpVPHvwedSLB7zkDlX2uAEgUDRc</span>\"\n  \"<span class=sf-dump-key>anggur_form_builder_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z7cuUWZvMHcel8crP64YTDt0e7CYtduXqK1Ps7cI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-353980444\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1110074256 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 11:08:48 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1110074256\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-966516442 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LrSn8kuzi0SFtXpVPHvwedSLB7zkDlX2uAEgUDRc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://127.0.0.1:8000/forms/form-pendataan-pelaku-usaha-kecil-IKyYpC/builder</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966516442\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/form/test-form-fdk8c4", "action_name": "form.show", "controller_action": "App\\Http\\Controllers\\FormSubmissionController@show"}, "badge": null}}