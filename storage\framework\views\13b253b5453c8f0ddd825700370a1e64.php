<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Form Builder: <?php echo e($form->title); ?>

            </h2>
            <div class="flex space-x-2">
                <a href="<?php echo e(route('form.show', $form->slug)); ?>" target="_blank" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Preview Form
                </a>
                <a href="<?php echo e(route('forms.index')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Forms
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <!-- Success Message -->
                    <div id="success-message" class="hidden bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                        <span id="success-text"></span>
                    </div>

                    <!-- Error Message -->
                    <div id="error-message" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        <span id="error-text"></span>
                    </div>

                    <div class="flex h-screen">
                        <!-- Field Types Sidebar -->
                        <div class="w-80 bg-gray-50 border-r border-gray-200 p-6 overflow-y-auto">
                            <h3 class="text-lg font-semibold mb-2 text-gray-800">Add Fields</h3>
                            <p class="text-sm text-gray-600 mb-6">Click to add fields to your form</p>
                            <div class="space-y-4" id="field-types">
                                <!-- Text Fields -->
                                <div class="field-category mb-6">
                                    <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">Text</h4>
                                    <div class="space-y-2">
                                        <div class="field-type bg-white border border-gray-200 rounded-lg p-3 cursor-pointer hover:border-blue-300 hover:shadow-sm transition-all duration-200" data-type="text">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                                    <i class="fas fa-font text-blue-600 text-sm"></i>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">Short text</div>
                                                    <div class="text-xs text-gray-500">Single line input</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="field-type bg-white border border-gray-200 rounded-lg p-3 cursor-pointer hover:border-blue-300 hover:shadow-sm transition-all duration-200" data-type="textarea">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                                    <i class="fas fa-align-left text-blue-600 text-sm"></i>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">Long text</div>
                                                    <div class="text-xs text-gray-500">Multi-line input</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="field-type bg-white border border-gray-200 rounded-lg p-3 cursor-pointer hover:border-blue-300 hover:shadow-sm transition-all duration-200" data-type="email">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                                    <i class="fas fa-envelope text-blue-600 text-sm"></i>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">Email</div>
                                                    <div class="text-xs text-gray-500">Email address</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Choice Fields -->
                                <div class="field-category mb-6">
                                    <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">Choice</h4>
                                    <div class="space-y-2">
                                        <div class="field-type bg-white border border-gray-200 rounded-lg p-3 cursor-pointer hover:border-blue-300 hover:shadow-sm transition-all duration-200" data-type="select">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                                    <i class="fas fa-chevron-down text-green-600 text-sm"></i>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">Dropdown</div>
                                                    <div class="text-xs text-gray-500">Select from list</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="field-type bg-white border border-gray-200 rounded-lg p-3 cursor-pointer hover:border-blue-300 hover:shadow-sm transition-all duration-200" data-type="radio">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                                    <i class="fas fa-dot-circle text-green-600 text-sm"></i>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">Multiple choice</div>
                                                    <div class="text-xs text-gray-500">Single selection</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="field-type bg-white border border-gray-200 rounded-lg p-3 cursor-pointer hover:border-blue-300 hover:shadow-sm transition-all duration-200" data-type="checkbox">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                                    <i class="fas fa-check-square text-green-600 text-sm"></i>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">Checkboxes</div>
                                                    <div class="text-xs text-gray-500">Multiple selection</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Date & Time Fields -->
                                <div class="field-category mb-6">
                                    <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">Date & Time</h4>
                                    <div class="space-y-2">
                                        <div class="field-type bg-white border border-gray-200 rounded-lg p-3 cursor-pointer hover:border-blue-300 hover:shadow-sm transition-all duration-200" data-type="date">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                                    <i class="fas fa-calendar text-yellow-600 text-sm"></i>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">Date</div>
                                                    <div class="text-xs text-gray-500">Pick a date</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="field-type bg-white border border-gray-200 rounded-lg p-3 cursor-pointer hover:border-blue-300 hover:shadow-sm transition-all duration-200" data-type="number">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                                    <i class="fas fa-hashtag text-purple-600 text-sm"></i>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">Number</div>
                                                    <div class="text-xs text-gray-500">Numeric input</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <!-- Form Builder Area -->
                        <div class="flex-1 bg-white">
                            <!-- Header -->
                            <div class="border-b border-gray-200 px-6 py-4">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <h2 class="text-xl font-semibold text-gray-900"><?php echo e($form->title); ?></h2>
                                        <p class="text-sm text-gray-600 mt-1">Build your form by clicking fields from the sidebar</p>
                                    </div>
                                    <div class="flex space-x-3">
                                        <button id="save-form" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                                            Save Form
                                        </button>
                                        <a href="<?php echo e(route('forms.preview', $form)); ?>" target="_blank" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                                            Preview
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Canvas -->
                            <div class="p-6 min-h-screen bg-gray-50">
                                <div class="max-w-2xl mx-auto">
                                    <!-- Empty State -->
                                    <div id="empty-state" class="text-center py-16">
                                        <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <i class="fas fa-plus text-gray-400 text-xl"></i>
                                        </div>
                                        <h3 class="text-lg font-medium text-gray-900 mb-2">Start building your form</h3>
                                        <p class="text-gray-600 mb-6">Click on any field type from the sidebar to add it to your form</p>
                                        <div class="flex justify-center">
                                            <div class="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <i class="fas fa-mouse-pointer mr-2 text-blue-500"></i>
                                                    Click fields to add them
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Form Fields Container -->
                                    <div id="form-fields" class="space-y-4"></div>
                                </div>
                            </div>

                            <!-- Conditional Logic Tab Content -->
                            <div id="conditions-tab" class="tab-content hidden">
                                <div class="bg-white border border-gray-300 rounded-lg p-4">
                                    <div class="mb-4">
                                        <button id="add-condition" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm">
                                            Add Condition Rule
                                        </button>
                                    </div>

                                    <div id="conditional-rules">
                                        <div id="no-conditions" class="text-center py-8 text-gray-500">
                                            <p>No conditional rules defined.</p>
                                            <p class="text-sm mt-1">Add rules to show/hide fields based on other field values.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Share & Embed Tab Content -->
                            <div id="share-tab" class="tab-content hidden">
                                <div class="bg-white border border-gray-300 rounded-lg p-4 space-y-6">
                                    <!-- Form URLs -->
                                    <div>
                                        <h3 class="text-lg font-semibold mb-4">Form URLs</h3>

                                        <!-- Default URL -->
                                        <div class="mb-4">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Default URL</label>
                                            <div class="flex">
                                                <input type="text" id="default-url" value="<?php echo e(route('form.show', $form->slug)); ?>"
                                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50" readonly>
                                                <button onclick="copyToClipboard('default-url')"
                                                        class="px-4 py-2 bg-blue-500 text-white rounded-r-md hover:bg-blue-600">
                                                    Copy
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Custom URL -->
                                        <div class="mb-4">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Custom URL</label>
                                            <div class="flex">
                                                <span class="px-3 py-2 bg-gray-100 border border-r-0 border-gray-300 rounded-l-md text-sm"><?php echo e(url('/f/')); ?>/</span>
                                                <input type="text" id="custom-url" value="<?php echo e($form->custom_url); ?>"
                                                       placeholder="my-custom-form"
                                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-r-md">
                                            </div>
                                            <p class="text-xs text-gray-500 mt-1">Leave empty to use default URL</p>
                                        </div>

                                        <!-- Short URL -->
                                        <div class="mb-4">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Short URL</label>
                                            <div class="flex">
                                                <input type="text" id="short-url"
                                                       value="<?php echo e($form->short_url ? url('/f/' . $form->short_url) : ''); ?>"
                                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50" readonly>
                                                <button onclick="generateShortUrl()"
                                                        class="px-4 py-2 bg-green-500 text-white rounded-r-md hover:bg-green-600">
                                                    <?php echo e($form->short_url ? 'Regenerate' : 'Generate'); ?>

                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Embed Code -->
                                    <div>
                                        <h3 class="text-lg font-semibold mb-4">Embed Code</h3>

                                        <div class="mb-4">
                                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-1">Width</label>
                                                    <input type="text" id="embed-width" value="100%"
                                                           class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                                </div>
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-1">Height</label>
                                                    <input type="text" id="embed-height" value="600px"
                                                           class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                                </div>
                                            </div>

                                            <textarea id="embed-code" rows="4"
                                                      class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 font-mono text-sm"
                                                      readonly></textarea>

                                            <div class="flex justify-between mt-2">
                                                <button onclick="updateEmbedCode()"
                                                        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">
                                                    Update Code
                                                </button>
                                                <button onclick="copyToClipboard('embed-code')"
                                                        class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-sm">
                                                    Copy Code
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Embed Settings -->
                                    <div>
                                        <h3 class="text-lg font-semibold mb-4">Embed Settings</h3>

                                        <div class="space-y-3">
                                            <label class="flex items-center">
                                                <input type="checkbox" id="is-embeddable" <?php echo e($form->is_embeddable ? 'checked' : ''); ?>

                                                       class="mr-2">
                                                <span class="text-sm">Allow form to be embedded</span>
                                            </label>

                                            <label class="flex items-center">
                                                <input type="checkbox" id="hide-header" class="mr-2">
                                                <span class="text-sm">Hide form header in embed</span>
                                            </label>

                                            <label class="flex items-center">
                                                <input type="checkbox" id="transparent-bg" class="mr-2">
                                                <span class="text-sm">Transparent background</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Field Properties Panel -->
                        <div class="col-span-3">
                            <h3 class="text-lg font-semibold mb-4">Field Properties</h3>
                            <div id="field-properties" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                <p class="text-gray-500 text-center">Select a field to edit its properties</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include form builder JavaScript -->
    <?php $__env->startPush('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    <script>
        // Form builder data
        let formFields = <?php echo json_encode($form->fields, 15, 512) ?>;
        let fieldCounter = formFields.length;

        // Initialize form builder
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a bit for SortableJS to load
            setTimeout(function() {
                initializeFormBuilder();
                loadExistingFields();
            }, 100);
        });
    </script>
    <script src="<?php echo e(asset('js/form-builder.js')); ?>"></script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\Projects\form-builder\resources\views/forms/builder.blade.php ENDPATH**/ ?>