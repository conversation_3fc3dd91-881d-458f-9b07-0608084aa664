<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo e($form->title); ?></title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body class="font-sans antialiased bg-gray-100">
    <div class="min-h-screen py-12">
        <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="mb-6">
                        <h1 class="text-2xl font-bold text-gray-900 mb-2"><?php echo e($form->title); ?></h1>
                        <?php if($form->description): ?>
                            <p class="text-gray-600"><?php echo e($form->description); ?></p>
                        <?php endif; ?>
                    </div>

                    <?php if($errors->any()): ?>
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                            <ul class="list-disc list-inside">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form action="<?php echo e(route('form.submit', $form->slug)); ?>" method="POST" id="public-form">
                        <?php echo csrf_field(); ?>
                        
                        <div class="space-y-6">
                            <?php $__currentLoopData = $form->fields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="form-field" data-field-id="<?php echo e($field->id); ?>" data-field-name="<?php echo e($field->name); ?>">
                                    <?php switch($field->type):
                                        case ('text'): ?>
                                        <?php case ('email'): ?>
                                        <?php case ('number'): ?>
                                            <div>
                                                <label for="<?php echo e($field->name); ?>" class="block text-sm font-medium text-gray-700 mb-1">
                                                    <?php echo e($field->label); ?>

                                                    <?php if($field->required): ?>
                                                        <span class="text-red-500">*</span>
                                                    <?php endif; ?>
                                                </label>
                                                <input type="<?php echo e($field->type); ?>" 
                                                       name="<?php echo e($field->name); ?>" 
                                                       id="<?php echo e($field->name); ?>"
                                                       value="<?php echo e(old($field->name)); ?>"
                                                       placeholder="<?php echo e($field->placeholder); ?>"
                                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                       <?php echo e($field->required ? 'required' : ''); ?>>
                                                <?php if($field->help_text): ?>
                                                    <p class="mt-1 text-sm text-gray-500"><?php echo e($field->help_text); ?></p>
                                                <?php endif; ?>
                                            </div>
                                            <?php break; ?>

                                        <?php case ('textarea'): ?>
                                            <div>
                                                <label for="<?php echo e($field->name); ?>" class="block text-sm font-medium text-gray-700 mb-1">
                                                    <?php echo e($field->label); ?>

                                                    <?php if($field->required): ?>
                                                        <span class="text-red-500">*</span>
                                                    <?php endif; ?>
                                                </label>
                                                <textarea name="<?php echo e($field->name); ?>" 
                                                          id="<?php echo e($field->name); ?>"
                                                          rows="4"
                                                          placeholder="<?php echo e($field->placeholder); ?>"
                                                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                          <?php echo e($field->required ? 'required' : ''); ?>><?php echo e(old($field->name)); ?></textarea>
                                                <?php if($field->help_text): ?>
                                                    <p class="mt-1 text-sm text-gray-500"><?php echo e($field->help_text); ?></p>
                                                <?php endif; ?>
                                            </div>
                                            <?php break; ?>

                                        <?php case ('select'): ?>
                                            <div>
                                                <label for="<?php echo e($field->name); ?>" class="block text-sm font-medium text-gray-700 mb-1">
                                                    <?php echo e($field->label); ?>

                                                    <?php if($field->required): ?>
                                                        <span class="text-red-500">*</span>
                                                    <?php endif; ?>
                                                </label>
                                                <select name="<?php echo e($field->name); ?>" 
                                                        id="<?php echo e($field->name); ?>"
                                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                        <?php echo e($field->required ? 'required' : ''); ?>>
                                                    <option value="">Choose an option</option>
                                                    <?php if($field->options): ?>
                                                        <?php $__currentLoopData = $field->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($option); ?>" <?php echo e(old($field->name) == $option ? 'selected' : ''); ?>>
                                                                <?php echo e($option); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php else: ?>
                                                        <option value="option1">Option 1</option>
                                                        <option value="option2">Option 2</option>
                                                    <?php endif; ?>
                                                </select>
                                                <?php if($field->help_text): ?>
                                                    <p class="mt-1 text-sm text-gray-500"><?php echo e($field->help_text); ?></p>
                                                <?php endif; ?>
                                            </div>
                                            <?php break; ?>

                                        <?php case ('radio'): ?>
                                            <div>
                                                <fieldset>
                                                    <legend class="block text-sm font-medium text-gray-700 mb-2">
                                                        <?php echo e($field->label); ?>

                                                        <?php if($field->required): ?>
                                                            <span class="text-red-500">*</span>
                                                        <?php endif; ?>
                                                    </legend>
                                                    <div class="space-y-2">
                                                        <?php if($field->options): ?>
                                                            <?php $__currentLoopData = $field->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <label class="flex items-center">
                                                                    <input type="radio" 
                                                                           name="<?php echo e($field->name); ?>" 
                                                                           value="<?php echo e($option); ?>"
                                                                           <?php echo e(old($field->name) == $option ? 'checked' : ''); ?>

                                                                           class="mr-2 text-blue-600 focus:ring-blue-500"
                                                                           <?php echo e($field->required ? 'required' : ''); ?>>
                                                                    <span><?php echo e($option); ?></span>
                                                                </label>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        <?php else: ?>
                                                            <label class="flex items-center">
                                                                <input type="radio" name="<?php echo e($field->name); ?>" value="option1" class="mr-2" <?php echo e($field->required ? 'required' : ''); ?>>
                                                                <span>Option 1</span>
                                                            </label>
                                                            <label class="flex items-center">
                                                                <input type="radio" name="<?php echo e($field->name); ?>" value="option2" class="mr-2" <?php echo e($field->required ? 'required' : ''); ?>>
                                                                <span>Option 2</span>
                                                            </label>
                                                        <?php endif; ?>
                                                    </div>
                                                </fieldset>
                                                <?php if($field->help_text): ?>
                                                    <p class="mt-1 text-sm text-gray-500"><?php echo e($field->help_text); ?></p>
                                                <?php endif; ?>
                                            </div>
                                            <?php break; ?>

                                        <?php case ('checkbox'): ?>
                                            <div>
                                                <fieldset>
                                                    <legend class="block text-sm font-medium text-gray-700 mb-2">
                                                        <?php echo e($field->label); ?>

                                                        <?php if($field->required): ?>
                                                            <span class="text-red-500">*</span>
                                                        <?php endif; ?>
                                                    </legend>
                                                    <div class="space-y-2">
                                                        <?php if($field->options): ?>
                                                            <?php $__currentLoopData = $field->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <label class="flex items-center">
                                                                    <input type="checkbox" 
                                                                           name="<?php echo e($field->name); ?>[]" 
                                                                           value="<?php echo e($option); ?>"
                                                                           <?php echo e(in_array($option, old($field->name, [])) ? 'checked' : ''); ?>

                                                                           class="mr-2 text-blue-600 focus:ring-blue-500">
                                                                    <span><?php echo e($option); ?></span>
                                                                </label>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        <?php else: ?>
                                                            <label class="flex items-center">
                                                                <input type="checkbox" name="<?php echo e($field->name); ?>[]" value="option1" class="mr-2">
                                                                <span>Option 1</span>
                                                            </label>
                                                            <label class="flex items-center">
                                                                <input type="checkbox" name="<?php echo e($field->name); ?>[]" value="option2" class="mr-2">
                                                                <span>Option 2</span>
                                                            </label>
                                                        <?php endif; ?>
                                                    </div>
                                                </fieldset>
                                                <?php if($field->help_text): ?>
                                                    <p class="mt-1 text-sm text-gray-500"><?php echo e($field->help_text); ?></p>
                                                <?php endif; ?>
                                            </div>
                                            <?php break; ?>

                                        <?php case ('date'): ?>
                                            <div>
                                                <label for="<?php echo e($field->name); ?>" class="block text-sm font-medium text-gray-700 mb-1">
                                                    <?php echo e($field->label); ?>

                                                    <?php if($field->required): ?>
                                                        <span class="text-red-500">*</span>
                                                    <?php endif; ?>
                                                </label>
                                                <input type="date" 
                                                       name="<?php echo e($field->name); ?>" 
                                                       id="<?php echo e($field->name); ?>"
                                                       value="<?php echo e(old($field->name)); ?>"
                                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                       <?php echo e($field->required ? 'required' : ''); ?>>
                                                <?php if($field->help_text): ?>
                                                    <p class="mt-1 text-sm text-gray-500"><?php echo e($field->help_text); ?></p>
                                                <?php endif; ?>
                                            </div>
                                            <?php break; ?>
                                    <?php endswitch; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <div class="mt-8">
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-md transition duration-200">
                                Submit Form
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Conditional Logic Script -->
    <?php if($form->conditionalRules->count() > 0): ?>
    <script>
        const conditionalRules = <?php echo json_encode($form->conditionalRules, 15, 512) ?>;
        
        document.addEventListener('DOMContentLoaded', function() {
            // Apply conditional logic
            conditionalRules.forEach(rule => {
                const conditionField = document.querySelector(`[name="${rule.condition_field.name}"]`);
                const targetField = document.querySelector(`[data-field-id="${rule.target_field_id}"]`);
                
                if (conditionField && targetField) {
                    // Initial check
                    checkCondition(rule, conditionField, targetField);
                    
                    // Add event listener
                    conditionField.addEventListener('change', function() {
                        checkCondition(rule, conditionField, targetField);
                    });
                }
            });
        });
        
        function checkCondition(rule, conditionField, targetField) {
            const fieldValue = conditionField.value;
            let showField = false;
            
            switch(rule.operator) {
                case 'equals':
                    showField = fieldValue === rule.condition_value;
                    break;
                case 'not_equals':
                    showField = fieldValue !== rule.condition_value;
                    break;
                case 'contains':
                    showField = fieldValue.toLowerCase().includes(rule.condition_value.toLowerCase());
                    break;
                case 'not_contains':
                    showField = !fieldValue.toLowerCase().includes(rule.condition_value.toLowerCase());
                    break;
            }
            
            if (rule.action === 'hide') {
                showField = !showField;
            }
            
            targetField.style.display = showField ? 'block' : 'none';
        }
    </script>
    <?php endif; ?>
</body>
</html>
<?php /**PATH D:\Projects\form-builder\resources\views/forms/public.blade.php ENDPATH**/ ?>